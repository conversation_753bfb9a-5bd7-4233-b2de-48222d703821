import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/job.dart';
import 'package:octasync_client/components/form/phone_input/phone_input.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
import 'package:octasync_client/models/role_mgmt/employee_department.dart';
import 'package:octasync_client/views/admin/organization/member_department/member_department_enum.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/position_model.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

// 人员创建
class CreateMemberDialog extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final Widget? child;

  const CreateMemberDialog({super.key, this.onSuccess, this.child});

  @override
  State<CreateMemberDialog> createState() => MemberDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class MemberDialogState extends State<CreateMemberDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  Employee _employeeData = Employee();

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  /// 职位选项
  List<DropdownItem> _getPositionOptions = [];

  /// 部门对应职位映射
  /// key: 部门id ,value: 职位id
  Map<String, String?> _depPositionMap = {};

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _employeeNameController = TextEditingController();
  final SelectController<int> _sexSelectController = SelectController<int>();
  final SelectController<String> _phoneAreaCodeController = SelectController<String>();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    _employeeData = Employee();
    _employeeNameController.text = '';
    _sexSelectController.clear();
    _phoneAreaCodeController.clear();
    _phoneNumberController.text = '';
    _emailController.text = '';
    checkedDepartments = [];
    _depPositionMap = {};
  }

  @override
  void initState() {
    super.initState();
    getList();
  }

  @override
  void dispose() {
    _employeeNameController.dispose();
    _sexSelectController.dispose();
    _phoneAreaCodeController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  /// 获取职位列表
  Future<void> getList() async {
    final res = await JobApi.getListPage({});
    final pages = PagesModel<PositionModel>.fromJson(
      res,
      (json) => PositionModel.fromJson(json as Map<String, dynamic>),
    );
    _getPositionOptions =
        pages.items.map((e) => DropdownItem(value: e.id, text: e.name ?? '')).toList();
  }

  /// 根据职位id获取职位选项
  DropdownItem? getPositionOptionsById(String? id) {
    return _getPositionOptions.firstWhereOrNull((e) => e.value == id);
  }

  /// 添加
  Future<void> createRequest(BuildContext context, StateSetter setDialogState) async {
    // 创建数据提交避免污染
    Employee newemployeeData = Employee.fromJson(_employeeData.toJson());
    newemployeeData.departmentList =
        checkedDepartments.map((t) {
          return EmployeeDepartment(
            departmentId: t.id,
            departmentName: t.departmentName,
            jobTitleId: _depPositionMap[t.id],
          );
        }).toList();

    print('提交>>>>>>>>>>${jsonEncode(newemployeeData.toJson())}');

    if (!_formKey.currentState!.validate()) return;

    // try {
    //   departmentModel.parentIdList = checkedDepartments.map((t) => t.id!).toList();
    //   final params = departmentModel.toJson();
    //   params.remove('Id');
    //   await DepartmentApi.add(params);
    //   ToastManager.success('提交成功');
    //   widget.onSuccess?.call();
    //   resetFormData();
    //   if (!isAddNext) context.pop();
    // } finally {
    //   setState(() {
    //     btnLoading = false;
    //   });
    // }
  }

  /// 打开添加部门弹窗
  void showDepartmentDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) {
    // 重置表单数据
    resetFormData();

    // 设置默认值
    _phoneAreaCodeController.setValue('+86');
    _employeeData.phoneAreaCode = '+86';

    _dialogType = type;

    double labelWidth = 85;

    /// 间距
    double spacing = 20;

    /// 名称
    Widget buildNameInput() {
      return AppInput(
        label: "姓名",
        labelWidth: labelWidth,
        required: true,
        labelPosition: LabelPosition.left,
        hintText: "姓名",
        size: InputSize.medium,
        controller: _employeeNameController,
        maxLength: 30,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入姓名';
          }
          return null;
        },
        onChanged: (value) {
          _employeeData.name = value;
        },
      );
    }

    /// 性别
    Widget buildSexSelect() {
      return AppFormField(
        label: '性别',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_sexSelectController.value == null) {
            return '请选择性别';
          }
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '性别',
              options: MemberDepartmentEnum.sexOptions,
              controller: _sexSelectController,
              onChanged: (value) {
                field.didChange(value);
                _employeeData.sexEnum = value;
              },
            ),
      );
    }

    /// 手机号
    Widget buildPhoneInput() {
      return AppFormField(
        label: '手机号码',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_employeeData.phoneAreaCode.isEmpty && _employeeData.phoneNumber.isEmpty) {
            return '区号和手机号不能为空';
          } else if (_employeeData.phoneAreaCode.isEmpty) {
            return '区号不能为空';
          } else if (_employeeData.phoneNumber.isEmpty) {
            return '手机号不能为空';
          }
          return null;
        },
        builder:
            (field) => PhoneInput(
              areaCodeController: _phoneAreaCodeController,
              phoneController: _phoneNumberController,
              onChange: (areaCode, phoneNumber) {
                field.didChange(phoneNumber);
                _employeeData.phoneAreaCode = areaCode ?? '+86';
                _employeeData.phoneNumber = phoneNumber ?? '';
              },
            ),
      );
    }

    /// 邮箱
    Widget buildEmailInput() {
      return AppInput(
        label: "工作邮箱",
        labelWidth: labelWidth,
        required: true,
        labelPosition: LabelPosition.left,
        hintText: "工作邮箱",
        size: InputSize.medium,
        controller: _emailController,
        maxLength: 30,
        validator: (value) {
          final regex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
          if (!regex.hasMatch(value!) || value.isEmpty) {
            return '请输入正确的工作邮箱';
          }
          return null;
        },
        onChanged: (value) {
          _employeeData.email = value;
        },
      );
    }

    /// 部门选择
    Widget buildDepartmentSelect(StateSetter setDialogState) {
      return AppFormField(
        label: '部门',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (checkedDepartments.isEmpty) {
            return '请选择部门';
          }
          return null;
        },
        builder:
            (field) => DepartmentSelector(
              checkStrictly: true,
              defaultCheckedDepartmentIds: checkedDepartments.map((t) => t.id!).toList(),
              onChange: (value) {
                field.didChange(value);
                setDialogState(() {
                  checkedDepartments = value;
                });
              },
            ),
      );
    }

    /// 职务
    Widget buildJobSelect() {
      String? selectedValue;

      return AppFormField(
        label: '职务',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          String? errMsg;
          if (checkedDepartments.isEmpty) {
            errMsg = '请选择部门';
          } else {
            if (!checkedDepartments.every((t) => _depPositionMap[t.id] != null)) {
              errMsg = '请选择职务';
            }
          }
          return errMsg;
        },
        builder:
            (field) => Container(
              width: double.infinity,
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                border: Border.all(color: context.border300),
                borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
              ),
              constraints: BoxConstraints(minHeight: 36),
              child: Column(
                spacing: 5,
                children:
                    checkedDepartments.map((t) {
                      return StatefulBuilder(
                        builder: (context, setPositionState) {
                          // 当前部门选中的职位
                          DropdownItem? curPositioned = getPositionOptionsById(
                            _depPositionMap[t.id],
                          );
                          return Container(
                            padding: const EdgeInsets.all(4),
                            color: context.background200,
                            child: Row(
                              children: [
                                Text(t.departmentName, style: TextStyle(fontSize: 12)),
                                const SizedBox(width: 8),
                                Container(width: 1, height: 16, color: context.border300),
                                const SizedBox(width: 8),
                                AppDropdown(
                                  text: curPositioned?.text ?? '请选择职务',
                                  items: _getPositionOptions,
                                  size: DropdownSize.small,
                                  onItemSelected: (item) {
                                    field.didChange(item);
                                    setPositionState(() {
                                      _depPositionMap[t.id!] = item.value;
                                    });
                                  },
                                  value: _depPositionMap[t.id],
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }).toList(),
              ),
            ),
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加成员' : '编辑成员',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (BuildContext context, StateSetter setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildNameInput(),
                      buildSexSelect(),
                      buildPhoneInput(),
                      buildEmailInput(),
                      buildDepartmentSelect(setDialogState),
                      buildJobSelect(),
                    ],
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
